# JobTailor - Requirements Specification

## Project Overview
JobTailor is a web-based application that optimizes resumes based on job descriptions using AI-powered analysis. The tool helps job seekers tailor their resumes to specific positions by leveraging the Gemini API for intelligent keyword extraction and content optimization.

## Functional Requirements

### 1. Job Description Input
- **FR-001**: Users must be able to input job descriptions via text paste
- **FR-002**: Users must be able to input job descriptions via URL
- **FR-003**: System must validate URL format and accessibility
- **FR-004**: System must extract job description text from provided URLs
- **FR-005**: System must handle common job board formats (LinkedIn, Indeed, etc.)

### 2. Resume Upload and Processing
- **FR-006**: Users must be able to upload resume files in PDF format
- **FR-007**: Users must be able to upload resume files in text format (.txt, .docx)
- **FR-008**: System must validate file types and sizes (max 10MB)
- **FR-009**: System must extract text content from uploaded resumes
- **FR-010**: System must preserve resume structure and formatting information

### 3. Gemini API Integration
- **FR-011**: Users must provide their own Gemini API key
- **FR-012**: System must validate API key format and functionality
- **FR-013**: System must send job descriptions to Gemini API for analysis
- **FR-014**: System must extract keywords, skills, and qualifications from job descriptions
- **FR-015**: System must handle API rate limits and errors gracefully

### 4. Resume Optimization
- **FR-016**: System must analyze resume content against job description keywords
- **FR-017**: System must prioritize resume sections based on keyword relevance
- **FR-018**: System must rewrite resume content to better match job requirements
- **FR-019**: System must maintain professional tone and accuracy
- **FR-020**: System must enforce two-page limit for optimized resumes

### 5. Output Generation
- **FR-021**: System must generate optimized resume in PDF format
- **FR-022**: System must provide downloadable resume file
- **FR-023**: System must display preview of optimized resume
- **FR-024**: System must show optimization summary and changes made

### 6. User Interface
- **FR-025**: Interface must be responsive and mobile-friendly
- **FR-026**: Interface must provide clear step-by-step workflow
- **FR-027**: Interface must show progress indicators during processing
- **FR-028**: Interface must display error messages and validation feedback

## Non-Functional Requirements

### 1. Performance
- **NFR-001**: Resume processing must complete within 60 seconds
- **NFR-002**: File uploads must support up to 10MB files
- **NFR-003**: System must handle concurrent users (minimum 10 simultaneous)
- **NFR-004**: API response times must be under 5 seconds for normal operations

### 2. Security
- **NFR-005**: User-provided API keys must be handled securely (not stored)
- **NFR-006**: Uploaded files must be temporarily stored and cleaned up
- **NFR-007**: All API communications must use HTTPS
- **NFR-008**: Input validation must prevent injection attacks

### 3. Reliability
- **NFR-009**: System must handle Gemini API failures gracefully
- **NFR-010**: System must provide meaningful error messages
- **NFR-011**: File processing must be fault-tolerant
- **NFR-012**: System must validate all user inputs

### 4. Usability
- **NFR-013**: Interface must be intuitive for non-technical users
- **NFR-014**: System must provide clear instructions and help text
- **NFR-015**: Error messages must be user-friendly and actionable
- **NFR-016**: Processing status must be clearly communicated

### 5. Compatibility
- **NFR-017**: Frontend must work on modern browsers (Chrome, Firefox, Safari, Edge)
- **NFR-018**: System must handle various resume formats and layouts
- **NFR-019**: PDF generation must be consistent across platforms

## Technical Constraints

### 1. Technology Stack
- **TC-001**: Frontend must use Next.js framework
- **TC-002**: Frontend styling must use Tailwind CSS
- **TC-003**: Backend must be implemented in Go
- **TC-004**: Must integrate with Google Gemini API

### 2. External Dependencies
- **TC-005**: Requires user-provided Gemini API key
- **TC-006**: Depends on external job board websites for URL scraping
- **TC-007**: Requires PDF processing libraries
- **TC-008**: Requires web scraping capabilities

### 3. Deployment
- **TC-009**: Application must be containerizable
- **TC-010**: Must support environment-based configuration
- **TC-011**: Must handle file storage for temporary processing

## Assumptions and Dependencies

### Assumptions
- Users have access to Gemini API keys from Google AI Studio
- Job descriptions contain sufficient detail for meaningful analysis
- Resume content is in English language
- Users understand basic file upload and form interactions

### Dependencies
- Google Gemini API availability and stability
- External job board websites maintaining accessible content
- PDF processing library compatibility
- Web scraping library effectiveness

## Success Criteria
- Users can successfully upload resumes and input job descriptions
- System generates optimized resumes that are relevant to job requirements
- Processing completes within acceptable time limits
- Generated resumes maintain professional quality and formatting
- User interface is intuitive and provides clear feedback
