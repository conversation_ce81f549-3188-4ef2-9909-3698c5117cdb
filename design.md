# JobTailor - System Design Document

## Architecture Overview

JobTailor follows a client-server architecture with a Next.js frontend and Go backend, integrated with the Google Gemini API for AI-powered resume optimization.

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js       │    │   Go Backend    │    │   Gemini API    │
│   Frontend      │◄──►│   Server        │◄──►│   Service       │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   Browser       │    │   File System   │
│   Storage       │    │   (Temporary)   │
└─────────────────┘    └─────────────────┘
```

## System Components

### 1. Frontend (Next.js + Tailwind CSS)

#### 1.1 Component Structure
```
src/
├── components/
│   ├── ui/
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   ├── FileUpload.tsx
│   │   └── ProgressBar.tsx
│   ├── forms/
│   │   ├── JobDescriptionForm.tsx
│   │   ├── ResumeUploadForm.tsx
│   │   └── ApiKeyForm.tsx
│   ├── layout/
│   │   ├── Header.tsx
│   │   ├── Footer.tsx
│   │   └── Layout.tsx
│   └── features/
│       ├── JobInput.tsx
│       ├── ResumeProcessor.tsx
│       └── ResultsDisplay.tsx
├── pages/
│   ├── index.tsx
│   ├── api/
│   │   └── proxy.ts
│   └── _app.tsx
├── hooks/
│   ├── useFileUpload.ts
│   ├── useJobProcessing.ts
│   └── useApiValidation.ts
└── utils/
    ├── validation.ts
    ├── fileHelpers.ts
    └── apiClient.ts
```

#### 1.2 Key Features
- **Responsive Design**: Mobile-first approach using Tailwind CSS
- **Progressive Enhancement**: Works without JavaScript for basic functionality
- **Real-time Validation**: Client-side validation with immediate feedback
- **File Handling**: Drag-and-drop file upload with preview
- **Progress Tracking**: Visual indicators for processing stages

#### 1.3 State Management
- React hooks for local component state
- Context API for global application state
- Custom hooks for complex logic (file upload, API calls)

### 2. Backend (Go)

#### 2.1 Project Structure
```
cmd/
└── server/
    └── main.go
internal/
├── api/
│   ├── handlers/
│   │   ├── job.go
│   │   ├── resume.go
│   │   └── health.go
│   ├── middleware/
│   │   ├── cors.go
│   │   ├── logging.go
│   │   └── validation.go
│   └── routes/
│       └── router.go
├── services/
│   ├── gemini/
│   │   ├── client.go
│   │   └── analyzer.go
│   ├── scraper/
│   │   ├── scraper.go
│   │   └── extractors.go
│   ├── resume/
│   │   ├── parser.go
│   │   ├── optimizer.go
│   │   └── generator.go
│   └── file/
│       ├── upload.go
│       └── processor.go
├── models/
│   ├── job.go
│   ├── resume.go
│   └── response.go
└── config/
    └── config.go
pkg/
├── pdf/
│   ├── parser.go
│   └── generator.go
└── utils/
    ├── validation.go
    └── helpers.go
```

#### 2.2 API Endpoints
```
POST /api/v1/jobs/analyze
- Input: Job description (text or URL) + API key
- Output: Extracted keywords and requirements

POST /api/v1/resumes/upload
- Input: Resume file (PDF/text)
- Output: Parsed resume content and metadata

POST /api/v1/resumes/optimize
- Input: Resume content + job analysis + API key
- Output: Optimized resume content

GET /api/v1/resumes/download/{id}
- Input: Resume ID
- Output: Generated PDF file

GET /api/v1/health
- Output: Service health status
```

#### 2.3 Core Services

**Gemini Service**
- API key validation
- Job description analysis
- Keyword extraction
- Resume optimization suggestions

**Scraper Service**
- URL validation and fetching
- HTML parsing for job descriptions
- Support for major job boards
- Error handling for inaccessible content

**Resume Service**
- PDF and text parsing
- Content structure analysis
- Optimization algorithm
- PDF generation with formatting

**File Service**
- Secure file upload handling
- Temporary file management
- File type validation
- Cleanup scheduling

### 3. Data Models

#### 3.1 Job Description Model
```go
type JobDescription struct {
    ID          string    `json:"id"`
    Content     string    `json:"content"`
    URL         string    `json:"url,omitempty"`
    Keywords    []string  `json:"keywords"`
    Skills      []string  `json:"skills"`
    Requirements []string `json:"requirements"`
    CreatedAt   time.Time `json:"created_at"`
}
```

#### 3.2 Resume Model
```go
type Resume struct {
    ID           string            `json:"id"`
    OriginalText string            `json:"original_text"`
    Sections     map[string]string `json:"sections"`
    OptimizedText string           `json:"optimized_text,omitempty"`
    Metadata     ResumeMetadata    `json:"metadata"`
    CreatedAt    time.Time         `json:"created_at"`
}

type ResumeMetadata struct {
    FileName     string `json:"file_name"`
    FileSize     int64  `json:"file_size"`
    FileType     string `json:"file_type"`
    PageCount    int    `json:"page_count"`
}
```

#### 3.3 Optimization Result Model
```go
type OptimizationResult struct {
    ID              string   `json:"id"`
    OriginalResumeID string  `json:"original_resume_id"`
    JobDescriptionID string  `json:"job_description_id"`
    OptimizedContent string  `json:"optimized_content"`
    Changes         []Change `json:"changes"`
    Score           float64  `json:"score"`
    CreatedAt       time.Time `json:"created_at"`
}

type Change struct {
    Section     string `json:"section"`
    Type        string `json:"type"` // added, modified, removed
    Description string `json:"description"`
}
```

## Integration Patterns

### 1. Gemini API Integration
```go
type GeminiClient struct {
    apiKey     string
    httpClient *http.Client
    baseURL    string
}

func (c *GeminiClient) AnalyzeJobDescription(ctx context.Context, jobDesc string) (*JobAnalysis, error)
func (c *GeminiClient) OptimizeResume(ctx context.Context, resume, jobDesc string) (*OptimizationSuggestions, error)
```

### 2. File Processing Pipeline
```
Upload → Validation → Parsing → Text Extraction → Processing → Optimization → PDF Generation → Download
```

### 3. Error Handling Strategy
- Graceful degradation for API failures
- Comprehensive input validation
- Structured error responses
- Logging and monitoring integration

## Security Considerations

### 1. Data Protection
- API keys handled in memory only (never stored)
- Temporary file cleanup after processing
- Input sanitization and validation
- HTTPS enforcement

### 2. File Security
- File type validation
- Size limits enforcement
- Virus scanning (future enhancement)
- Secure temporary storage

### 3. API Security
- Rate limiting implementation
- Request validation
- CORS configuration
- Input sanitization

## Performance Optimization

### 1. Frontend Optimization
- Code splitting and lazy loading
- Image optimization
- Caching strategies
- Bundle size optimization

### 2. Backend Optimization
- Connection pooling
- Concurrent processing
- Memory management
- Efficient file handling

### 3. Scalability Considerations
- Stateless design
- Horizontal scaling capability
- Load balancing support
- Database-free architecture

## Deployment Architecture

### 1. Development Environment
- Docker containers for consistent development
- Hot reloading for rapid iteration
- Environment variable configuration
- Local file storage

### 2. Production Environment
- Container orchestration (Docker/Kubernetes)
- Load balancer configuration
- Monitoring and logging
- Backup and recovery procedures

## Technology Stack Details

### Frontend Dependencies
- Next.js 14+ (React framework)
- Tailwind CSS 3+ (Styling)
- TypeScript (Type safety)
- React Hook Form (Form handling)
- Axios (HTTP client)

### Backend Dependencies
- Go 1.21+ (Runtime)
- Gin (HTTP framework)
- Colly (Web scraping)
- UniPDF (PDF processing)
- Testify (Testing)

### External Services
- Google Gemini API (AI processing)
- Job board websites (Content source)
